package com.drxin.bizz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.drxin.bizz.constants.UserTypeRoleMapConstants;
import com.drxin.bizz.domain.ContributionEventLog;
import com.drxin.bizz.domain.MemberInfo;
import com.drxin.bizz.mapper.MemberInfoMapper;
import com.drxin.bizz.service.IMemberInfoService;
import com.drxin.bizz.vo.MemberUpdateTypeVo;
import com.drxin.bizz.vo.MemberAiderStatVo;
import com.drxin.bizz.vo.MemberAiderStatExportVo;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.exception.ServiceException;
import com.drxin.common.utils.SecurityUtils;
import com.drxin.common.utils.StringUtils;
import com.drxin.framework.event.ContributionActionEvent;
import com.drxin.system.domain.SysUserRole;
import com.drxin.system.mapper.SysUserMapper;
import com.drxin.system.mapper.SysUserRoleMapper;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MemberInfoServiceImpl extends ServiceImpl<MemberInfoMapper, MemberInfo> implements IMemberInfoService {
    @Resource
    private SysUserRoleMapper userRoleMapper;

    @Resource
    private SysUserMapper userMapper;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Override
    public List<MemberInfo> selectMemberInfoList(MemberInfo memberInfo) {
        return baseMapper.selectMemberInfoList(memberInfo);
    }

    @Override
    public MemberInfo selectMemberInfoById(Long id) {
        return baseMapper.selectMemberInfoById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMemberType(MemberUpdateTypeVo memberUpdateTypeVo) {
        // 获取修改用户的id
        List<Long> ids = memberUpdateTypeVo.getIds();
        // 判断成交人是否是自己
        if (ids.contains(memberUpdateTypeVo.getDealInviterId())) {
            throw new ServiceException("成交人不能是自己");
        }

        // 检查成交人循环
        if (baseMapper.checkDealInviterCycle(ids, memberUpdateTypeVo.getDealInviterId()) > 0) {
            throw new ServiceException("设置成交人会形成循环引用，请检查成交人关系");
        }

        // 获取修改后的用户类型（支持多个类型，逗号分割）
        String userTypes = memberUpdateTypeVo.getUpdateUserType();
        if (StringUtils.isBlank(userTypes)) {
            throw new ServiceException("用户类型不能为空");
        }

        // 解析用户类型
        List<String> typeList = Arrays.stream(userTypes.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (typeList.isEmpty()) {
            throw new ServiceException("用户类型不能为空");
        }

        // 删除用户原有的角色
        userRoleMapper.deleteUserRole(ids.toArray(new Long[0]));

        // 获取所有类型对应的角色ID
        Set<Integer> roleIds = new HashSet<>();
        for (String type : typeList) {
            try {
                UserTypeRoleMapConstants userTypeRoleMapConstants = UserTypeRoleMapConstants.valueOf(type.toUpperCase());
                roleIds.add(userTypeRoleMapConstants.getRoleId());
            } catch (IllegalArgumentException e) {
                throw new ServiceException("不支持的用户类型: " + type);
            }
        }

        // 更改用户的用户类型（存储为逗号分割的字符串）
        UpdateWrapper<SysUser> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("user_id", ids);
        updateWrapper.set("user_type", userTypes); // 直接存储完整的类型字符串
        updateWrapper.set("deal_inviter_id", memberUpdateTypeVo.getDealInviterId());
        userMapper.update(null, updateWrapper);

        // 批量插入用户角色关系
        List<SysUserRole> sysUserRoleList = new ArrayList<>();
        for (Long userId : ids) {
            for (Integer roleId : roleIds) {
                sysUserRoleList.add(new SysUserRole(userId, roleId));
            }
        }

        if (!sysUserRoleList.isEmpty()) {
            userRoleMapper.batchUserRole(sysUserRoleList);
        }

        // 处理弟子相关逻辑
        if (containsDisciple(userTypes)) {
            handleDiscipleLogic(ids, memberUpdateTypeVo.getDealInviterId());
        }

        return 1;
    }

    @Override
    public List<MemberInfo> selectMemberInfoListToSelect(MemberUpdateTypeVo memberUpdateTypeVo) {
        String keyword = memberUpdateTypeVo.getKeyWord();
        QueryWrapper<MemberInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("user_id", "real_name", "id_card", "phonenumber");
        if (memberUpdateTypeVo.getUpdateUserType() != null) {
            List<String> typeList = Arrays.stream(memberUpdateTypeVo.getUpdateUserType().split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            if (!typeList.isEmpty() && typeList.size() > 1) {
                queryWrapper.in("user_type", typeList);
            }else if (!typeList.isEmpty()) {
                queryWrapper.like("user_type", typeList.get(0));
            }

        }
        queryWrapper.isNotNull("user_type");
        queryWrapper.isNotNull("real_name");
        if (StringUtils.isNotEmpty(keyword)) {
            queryWrapper.and(wrapper -> wrapper.like("real_name", keyword)
                    .or()
                    .eq("id_card", keyword).or().eq("nick_name", keyword));
        }

        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 更新成员成交人
     * 注意：方法名为updateMemberInviter但实际更新的是成交人(deal_inviter_id)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMemberInviter(String ids, String newInviterId) {
        if (StringUtils.isBlank(ids)) {
            throw new ServiceException("用户ID不能为空");
        }
        if (StringUtils.isBlank(newInviterId)) {
            throw new ServiceException("新成交人ID不能为空");
        }

        // 解析用户ID列表
        List<Long> userIds = Arrays.stream(ids.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toList());

        if (userIds.isEmpty()) {
            throw new ServiceException("用户ID不能为空");
        }

        // 检查新成交人是否存在
        SysUser newDealInviter = userMapper.selectById(Long.valueOf(newInviterId));
        if (newDealInviter == null) {
            throw new ServiceException("新成交人不存在");
        }

        // 检查是否包含新成交人自己
        if (userIds.contains(Long.valueOf(newInviterId))) {
            throw new ServiceException("不能将用户设置为自己的成交人");
        }

        int count = baseMapper.checkDealInviterCycle(userIds, Long.valueOf(newInviterId));
        // 检查成交人循环
        if ( count > 0) {
            throw new ServiceException("设置成交人会形成循环引用，请检查成交人关系");
        }

        // 批量更新成交人
        UpdateWrapper<SysUser> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("user_id", userIds);
        updateWrapper.set("deal_inviter_id", newInviterId);

        int result = userMapper.update(null, updateWrapper);

        // 发布成交人团队变更事件
        if (result > 0) {
            publishDealInviterTeamChangeEvent(userIds, Long.valueOf(newInviterId));
        }
        return result;
    }

    @Override
    public List<MemberInfo> getRecommendList(MemberInfo memberInfo) {
        QueryWrapper<MemberInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("deal_inviter_id", memberInfo.getUserId());
        if (memberInfo.getRealName() != null) {
            queryWrapper.like("real_name", memberInfo.getRealName());
        }
        queryWrapper.select("real_name", "user_type", "upgraded_time");
        queryWrapper.orderByDesc("upgraded_time");
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<MemberAiderStatVo> selectMemberAiderStatList(MemberAiderStatVo memberAiderStatVo) {
        return baseMapper.selectMemberAiderStatList(memberAiderStatVo);
    }

    @Override
    public List<MemberAiderStatExportVo> selectMemberAiderStatExportList(MemberAiderStatVo memberAiderStatVo) {
        // 复用现有查询方法
        List<MemberAiderStatVo> statList = baseMapper.selectMemberAiderStatList(memberAiderStatVo);

        // 转换为导出VO
        return statList.stream().map(stat -> {
            MemberAiderStatExportVo exportVo = new MemberAiderStatExportVo();
            exportVo.setRealName(stat.getRealName());
            exportVo.setUserType(stat.getUserType());
            exportVo.setAiderCount(stat.getAiderCount());
            return exportVo;
        }).collect(Collectors.toList());
    }

    /**
     * 检查用户类型是否包含弟子
     *
     * @param userTypes 用户类型字符串（逗号分隔）
     * @return 是否包含弟子
     */
    private boolean containsDisciple(String userTypes) {
        if (StringUtils.isBlank(userTypes)) {
            return false;
        }

        // userTypes是逗号分隔的字符串，检查是否包含disciple
        String[] typeArray = userTypes.split(",");
        for (String type : typeArray) {
            if ("disciple".equals(type.trim())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 处理弟子相关逻辑
     *
     * @param userIds 用户ID列表
     * @param dealInviterId 成交人ID
     */
    private void handleDiscipleLogic(List<Long> userIds, Long dealInviterId) {
        try {
            // 获取当前操作用户ID作为事件发布者
            Long currentUserId = SecurityUtils.getUserId();

            // 1. 发布身份弟子贡献值事件
            publishIdentityDiscipleEvent(currentUserId, userIds);

            // 2. 如果包含成交人，发布推荐成功弟子贡献值事件
            if (dealInviterId != null) {
                publishRecommendSuccessDiscipleEvent(currentUserId, userIds, dealInviterId);
            }

            // 3. 发布团队退出事件
            publishTeamExitEvent(currentUserId, userIds);

        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.error("处理弟子相关逻辑失败: userIds={}, dealInviterId={}", userIds, dealInviterId, e);
        }
    }

    /**
     * 发布身份弟子贡献值事件
     *
     * @param currentUserId 当前用户ID
     * @param userIds 用户ID列表
     */
    private void publishIdentityDiscipleEvent(Long currentUserId, List<Long> userIds) {
        try {
            // 构建事件上下文
            Map<String, Object> context = new HashMap<>();
            context.put("successApplyIds", userIds); // 使用用户ID作为申请ID

            // 创建贡献值事件
            ContributionActionEvent event = new ContributionActionEvent(
                this,
                currentUserId,
                ContributionEventLog.ACTION_IDENTITY_DISCIPLE,
                context
            );

            eventPublisher.publishEvent(event);

            log.info("身份弟子贡献值事件发布成功: userIds={}", userIds);

        } catch (Exception e) {
            log.error("发布身份弟子贡献值事件失败: userIds={}", userIds, e);
        }
    }

    /**
     * 发布推荐成功弟子贡献值事件
     *
     * @param currentUserId 当前用户ID
     * @param userIds 用户ID列表
     * @param dealInviterId 成交人ID
     */
    private void publishRecommendSuccessDiscipleEvent(Long currentUserId, List<Long> userIds, Long dealInviterId) {
        try {
            // 构建推荐信息列表
            List<Map<String, Object>> recommendInfoList = buildRecommendInfoList(userIds, dealInviterId);

            if (recommendInfoList.isEmpty()) {
                log.warn("推荐信息列表为空，跳过发布推荐成功弟子贡献值事件");
                return;
            }

            // 构建事件上下文
            Map<String, Object> context = new HashMap<>();
            context.put("recommendInfoList", recommendInfoList);

            // 创建贡献值事件
            ContributionActionEvent event = new ContributionActionEvent(
                this,
                currentUserId,
                ContributionEventLog.ACTION_RECOMMEND_SUCCESS_DISCIPLE,
                context
            );

            eventPublisher.publishEvent(event);

            log.info("推荐成功弟子贡献值事件发布成功: userIds={}, dealInviterId={}", userIds, dealInviterId);

        } catch (Exception e) {
            log.error("发布推荐成功弟子贡献值事件失败: userIds={}, dealInviterId={}", userIds, dealInviterId, e);
        }
    }

    /**
     * 发布团队退出事件
     *
     * @param currentUserId 当前用户ID
     * @param userIds 用户ID列表
     */
    private void publishTeamExitEvent(Long currentUserId, List<Long> userIds) {
        try {
            // 构建事件上下文
            Map<String, Object> context = new HashMap<>();
            context.put("userIds", userIds);

            // 创建贡献值事件
            ContributionActionEvent event = new ContributionActionEvent(
                this,
                currentUserId,
                ContributionEventLog.ACTION_TEAM_EXIT,
                context
            );

            eventPublisher.publishEvent(event);

            log.info("团队退出事件发布成功: userIds={}", userIds);

        } catch (Exception e) {
            log.error("发布团队退出事件失败: userIds={}", userIds, e);
        }
    }

    /**
     * 构建推荐信息列表
     *
     * @param userIds 用户ID列表
     * @param dealInviterId 成交人ID
     * @return 推荐信息列表
     */
    private List<Map<String, Object>> buildRecommendInfoList(List<Long> userIds, Long dealInviterId) {
        List<Map<String, Object>> recommendInfoList = new ArrayList<>();

        for (Long userId : userIds) {
            try {
                // 查询用户信息
                SysUser user = userMapper.selectById(userId);
                if (user == null) {
                    log.warn("用户不存在，跳过: userId={}", userId);
                    continue;
                }

                // 构建推荐信息
                Map<String, Object> recommendInfo = new HashMap<>();
                recommendInfo.put("applyId", userId); // 使用用户ID作为申请ID
                recommendInfo.put("userId", userId);
                recommendInfo.put("userType", user.getUserType());
                recommendInfo.put("inviterId", user.getInviterId()); // 推荐人ID
                recommendInfo.put("dealInviterId", dealInviterId); // 成交人ID
                recommendInfo.put("realName", user.getRealName());

                recommendInfoList.add(recommendInfo);

            } catch (Exception e) {
                log.error("构建推荐信息失败: userId={}", userId, e);
            }
        }

        return recommendInfoList;
    }

    /**
     * 发布成交人团队变更事件
     *
     * @param userIds 用户ID列表
     * @param newDealInviterId 新成交人ID
     */
    private void publishDealInviterTeamChangeEvent(List<Long> userIds, Long newDealInviterId) {
        try {
            log.info("开始发布成交人团队变更事件: userIds={}, newDealInviterId={}", userIds, newDealInviterId);

            // 构建事件上下文
            Map<String, Object> context = new HashMap<>();
            context.put("userIds", userIds);
            context.put("newDealInviterId", newDealInviterId);

            // 获取当前操作用户ID作为事件发布者
            Long currentUserId = SecurityUtils.getUserId();

            // 创建并发布成交人团队变更事件
            ContributionActionEvent event = new ContributionActionEvent(
                this, currentUserId, "DEAL_INVITER_TEAM_CHANGE", context);
            eventPublisher.publishEvent(event);

            log.info("成交人团队变更事件发布成功: userIds={}, newDealInviterId={}", userIds, newDealInviterId);

        } catch (Exception e) {
            log.error("发布成交人团队变更事件失败: userIds={}, newDealInviterId={}", userIds, newDealInviterId, e);
        }
    }

}
