package com.drxin.bizz.strategy.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.drxin.bizz.domain.Team;
import com.drxin.bizz.domain.TeamMember;
import com.drxin.bizz.mapper.TeamMapper;
import com.drxin.bizz.mapper.TeamMemberMapper;
import com.drxin.bizz.service.ITeamMemberService;
import com.drxin.bizz.strategy.ContributionStrategy;
import com.drxin.bizz.utils.UserHierarchyUtils;
import com.drxin.common.core.domain.entity.SysUser;
import com.drxin.common.utils.DateUtils;
import com.drxin.common.utils.StringUtils;
import com.drxin.framework.event.ContributionActionEvent;
import com.drxin.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 成交人团队变更策略
 * 
 * 当修改成交人时，成员及其所有下级都变更为成交人所在团队
 * 如果成交人是弟子，需要查询team表获取团队信息
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Component
public class DealInviterTeamChangeStrategy implements ContributionStrategy {

    @Autowired
    private ITeamMemberService teamMemberService;

    @Resource
    private TeamMemberMapper teamMemberMapper;

    @Resource
    private TeamMapper teamMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private UserHierarchyUtils userHierarchyUtils;

    @Override
    public String getSupportAction() {
        return "DEAL_INVITER_TEAM_CHANGE";
    }

    @Override
    public void process(ContributionActionEvent event) {
        try {
            Map<String, Object> context = event.getContext();
            @SuppressWarnings("unchecked")
            List<Long> userIds = (List<Long>) context.get("userIds");
            Long newDealInviterId = (Long) context.get("newDealInviterId");

            if (userIds == null || userIds.isEmpty()) {
                log.warn("用户ID列表为空，跳过成交人团队变更处理");
                return;
            }

            if (newDealInviterId == null) {
                log.warn("新成交人ID为空，跳过成交人团队变更处理");
                return;
            }

            log.info("开始处理成交人团队变更: userIds={}, newDealInviterId={}", userIds, newDealInviterId);

            // 查询成交人的团队信息
            String dealInviterTeamId = getDealInviterTeamId(newDealInviterId);
            
            // 处理每个用户及其下级的团队变更
            int totalProcessed = 0;
            for (Long userId : userIds) {
                int processed = processUserAndSubordinatesTeamChange(userId, dealInviterTeamId);
                totalProcessed += processed;
            }

            log.info("成交人团队变更处理完成: 处理用户数={}, 总变更数={}", userIds.size(), totalProcessed);

        } catch (Exception e) {
            log.error("处理成交人团队变更失败: userId={}, actionCode={}", 
                event.getUserId(), event.getActionCode(), e);
            throw e; // 重新抛出异常，让监听器记录失败状态
        }
    }

    /**
     * 获取成交人的团队ID
     * 如果成交人是弟子，查询team表；否则查询team_member表
     * 
     * @param dealInviterId 成交人ID
     * @return 团队ID，如果没有团队则返回"-1"
     */
    private String getDealInviterTeamId(Long dealInviterId) {
        try {
            // 查询成交人信息
            SysUser dealInviter = sysUserMapper.selectById(dealInviterId);
            if (dealInviter == null) {
                log.warn("成交人不存在: dealInviterId={}", dealInviterId);
                return "-1";
            }

            // 检查成交人是否是弟子
            if (userHierarchyUtils.containsDisciple(dealInviter.getUserType())) {
                // 成交人是弟子，查询team表
                LambdaQueryWrapper<Team> teamQuery = new LambdaQueryWrapper<>();
                teamQuery.eq(Team::getLeaderId, dealInviterId.toString());
                Team team = teamMapper.selectOne(teamQuery);
                
                if (team != null) {
                    log.info("成交人是弟子且有团队: dealInviterId={}, teamId={}", dealInviterId, team.getTeamId());
                    return team.getTeamId().toString();
                } else {
                    log.info("成交人是弟子但没有团队: dealInviterId={}", dealInviterId);
                    return "-1";
                }
            } else {
                // 成交人不是弟子，查询team_member表
                String teamId = teamMemberMapper.selectTeamIdByMemberId(dealInviterId.toString());
                if (teamId != null && !teamId.trim().isEmpty()) {
                    log.info("成交人在团队中: dealInviterId={}, teamId={}", dealInviterId, teamId);
                    return teamId;
                } else {
                    log.info("成交人不在任何团队中: dealInviterId={}", dealInviterId);
                    return "-1";
                }
            }

        } catch (Exception e) {
            log.error("获取成交人团队ID失败: dealInviterId={}", dealInviterId, e);
            return "-1";
        }
    }

    /**
     * 处理用户及其下级的团队变更
     * 
     * @param userId 用户ID
     * @param targetTeamId 目标团队ID
     * @return 处理的用户数量
     */
    private int processUserAndSubordinatesTeamChange(Long userId, String targetTeamId) {
        try {
            log.info("开始处理用户及下级团队变更: userId={}, targetTeamId={}", userId, targetTeamId);

            // 获取所有需要变更团队的用户（包括该用户及其所有下级）
            List<String> allChangeUserIds = new ArrayList<>();

            // 递归查找该用户的所有下级（遇到disciple时停止搜索）
            List<SysUser> subordinates = userHierarchyUtils.findAllSubordinates(userId.toString());

            // 将下级用户ID转换为字符串列表
            for (SysUser subordinate : subordinates) {
                allChangeUserIds.add(subordinate.getUserId().toString());
            }

            // 将当前用户也加入变更列表
            allChangeUserIds.add(userId.toString());

            // 批量更新团队成员信息
            int processedCount = batchUpdateTeamMembers(allChangeUserIds, targetTeamId);
            
            log.info("用户及下级团队变更完成: userId={}, 变更成员数量={}", userId, processedCount);
            return processedCount;

        } catch (Exception e) {
            log.error("处理用户及下级团队变更失败: userId={}, targetTeamId={}", userId, targetTeamId, e);
            return 0;
        }
    }

    /**
     * 批量更新团队成员信息
     * 
     * @param userIds 用户ID列表
     * @param targetTeamId 目标团队ID
     * @return 成功更新的数量
     */
    private int batchUpdateTeamMembers(List<String> userIds, String targetTeamId) {
        if (userIds.isEmpty()) {
            log.info("没有需要更新团队的用户");
            return 0;
        }

        int successCount = 0;
        for (String userId : userIds) {
            try {
                // 检查用户是否已在团队中
                String existingTeamId = teamMemberMapper.selectTeamIdByMemberId(userId);
                
                if (existingTeamId != null && !existingTeamId.trim().isEmpty()) {
                    // 用户已在团队中，更新团队ID
                    TeamMember updateTeamMember = new TeamMember();
                    updateTeamMember.setMemberId(userId);
                    updateTeamMember.setTeamId(targetTeamId);
                    
                    int updateResult = teamMemberService.updateTeamMember(updateTeamMember);
                    if (updateResult > 0) {
                        log.debug("用户团队更新成功: userId={}, 从团队{}变更到团队{}", 
                            userId, existingTeamId, targetTeamId);
                        successCount++;
                    } else {
                        log.error("用户团队更新失败: userId={}", userId);
                    }
                } else {
                    // 用户不在任何团队中，创建新的团队成员记录
                    SysUser user = sysUserMapper.selectById(Long.valueOf(userId));
                    if (user != null) {
                        TeamMember newTeamMember = new TeamMember();
                        newTeamMember.setMemberId(userId);
                        newTeamMember.setTeamId(targetTeamId);
                        newTeamMember.setMemberName(StringUtils.isNotEmpty(user.getRealName()) ? 
                            user.getRealName() : user.getNickName());
                        newTeamMember.setMemberType(user.getUserType());
                        newTeamMember.setUpgradedTime(user.getUpgradedTime());
                        
                        int insertResult = teamMemberService.insertTeamMember(newTeamMember);
                        if (insertResult > 0) {
                            log.debug("用户加入团队成功: userId={}, teamId={}", userId, targetTeamId);
                            successCount++;
                        } else {
                            log.error("用户加入团队失败: userId={}, teamId={}", userId, targetTeamId);
                        }
                    }
                }
                
            } catch (Exception e) {
                log.error("更新用户团队信息失败: userId={}, targetTeamId={}", userId, targetTeamId, e);
            }
        }
        
        log.info("批量更新团队成员完成: 总数={}, 成功={}", userIds.size(), successCount);
        return successCount;
    }
}
